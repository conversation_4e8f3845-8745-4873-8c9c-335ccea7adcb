import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/app_provider.dart';
import '../models/media_item.dart';
import '../widgets/floating_instruments.dart';
import '../widgets/pulsing_circle.dart';
import 'song_player_view.dart';
import 'video_player_view.dart';

class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen> {
  String _searchQuery = '';
  String _filterType = 'all'; // 'all', 'audio', 'video'
  String _sortBy = 'name'; // 'name', 'artist', 'dateAdded'

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, app, _) {
        final filteredItems = _getFilteredItems(app.library);
        
        return Stack(
          children: [
            // Background floating instruments
            const FloatingInstruments(
              count: 4,
              speed: 0.4,
              instruments: ['🎵', '🎶', '🎼', '🎤'],
            ),
            
            // Main content
            Column(
              children: [
                // Search and filter bar
                _buildSearchAndFilterBar(),
                
                // Add media button
                _buildAddMediaButton(context, app),
                
                // Library stats
                _buildLibraryStats(context, app),
                
                // Media items list
                Expanded(
                  child: filteredItems.isEmpty
                      ? _buildEmptyState(context, app)
                      : _buildMediaList(context, app, filteredItems),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search your library...',
                hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
                prefixIcon: Icon(Icons.search, color: Colors.white.withOpacity(0.8)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 12),
          
          // Filter and sort controls
          Row(
            children: [
              // Filter dropdown
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: DropdownButtonFormField<String>(
                    initialValue: _filterType,
                    style: const TextStyle(color: Colors.white),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    dropdownColor: Colors.black.withOpacity(0.9),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Media')),
                      DropdownMenuItem(value: 'audio', child: Text('Audio Only')),
                      DropdownMenuItem(value: 'video', child: Text('Video Only')),
                    ],
                    onChanged: (value) => setState(() => _filterType = value!),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              
              // Sort dropdown
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: DropdownButtonFormField<String>(
                    initialValue: _sortBy,
                    style: const TextStyle(color: Colors.white),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    dropdownColor: Colors.black.withOpacity(0.9),
                    items: const [
                      DropdownMenuItem(value: 'name', child: Text('Sort by Name')),
                      DropdownMenuItem(value: 'artist', child: Text('Sort by Artist')),
                      DropdownMenuItem(value: 'dateAdded', child: Text('Sort by Date')),
                    ],
                    onChanged: (value) => setState(() => _sortBy = value!),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddMediaButton(BuildContext context, AppProvider app) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0.2),
                    Colors.white.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: TextButton.icon(
                onPressed: () => _addMedia(context, app),
                icon: const Icon(Icons.add, color: Colors.white),
                label: const Text('Add Media', style: TextStyle(color: Colors.white)),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLibraryStats(BuildContext context, AppProvider app) {
    final audioCount = app.library.where((item) => !item.isVideo).length;
    final videoCount = app.library.where((item) => item.isVideo).length;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(context, 'Total', app.library.length, Icons.library_music),
          _buildStatItem(context, 'Audio', audioCount, Icons.music_note),
          _buildStatItem(context, 'Video', videoCount, Icons.videocam),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, int count, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.white),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context, AppProvider app) {
    return Stack(
      children: [
        // Additional floating instruments for empty state
        const FloatingInstruments(
          count: 6,
          speed: 0.6,
          instruments: ['🎸', '🎹', '🎷', '🥁', '🎻', '🎺'],
        ),
        
        // Main content
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Pulsing library icon
              PulsingCircle(
                size: 120,
                color: Theme.of(context).colorScheme.primary,
                duration: const Duration(milliseconds: 2000),
              ),
              const SizedBox(height: 32),
              
              Text(
                app.library.isEmpty ? 'Your library is empty' : 'No matching items',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                app.library.isEmpty 
                    ? 'Add some media files to get started'
                    : 'Try adjusting your search or filters',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
              if (app.library.isEmpty) ...[
                const SizedBox(height: 32),
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0.2),
                        Colors.white.withOpacity(0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                  child: TextButton.icon(
                    onPressed: () => _addMedia(context, app),
                    icon: const Icon(Icons.add, color: Colors.white),
                    label: const Text('Add Your First Media', style: TextStyle(color: Colors.white)),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMediaList(BuildContext context, AppProvider app, List<MediaItem> items) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildMediaItem(context, app, item, index);
      },
    );
  }

  Widget _buildMediaItem(BuildContext context, AppProvider app, MediaItem item, int index) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 200 + (index * 50)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.1)),
              ),
              child: ListTile(
                leading: Container(
                  decoration: BoxDecoration(
                    color: item.isVideo 
                        ? Colors.red.withOpacity(0.2)
                        : Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: (item.isVideo ? Colors.red : Colors.blue).withOpacity(0.2),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Icon(
                    item.isVideo ? Icons.videocam : Icons.music_note,
                    color: item.isVideo ? Colors.red : Colors.blue,
                  ),
                ),
                title: Text(
                  item.displayTitle,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.white),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.displaySubtitle,
                      style: TextStyle(color: Colors.white.withOpacity(0.7)),
                    ),
                    if (item.duration != null)
                      Text(
                        _formatDuration(item.duration!),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withOpacity(0.6),
                        ),
                      ),
                  ],
                ),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) => _handleMediaAction(context, app, item, value),
                  icon: Icon(Icons.more_vert, color: Colors.white.withOpacity(0.8)),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'play',
                      child: Row(
                        children: [
                          Icon(Icons.play_arrow),
                          SizedBox(width: 8),
                          Text('Play Now'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'info',
                      child: Row(
                        children: [
                          Icon(Icons.info),
                          SizedBox(width: 8),
                          Text('Media Info'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    ...app.playlists.map((playlist) => PopupMenuItem(
                      value: 'add_to_${playlist.id}',
                      child: Row(
                        children: [
                          const Icon(Icons.playlist_add),
                          const SizedBox(width: 8),
                          Text('Add to ${playlist.name}'),
                        ],
                      ),
                    )),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
                onTap: () => _navigateToPlayer(context, item),
              ),
            ),
          ),
        );
      },
    );
  }

  List<MediaItem> _getFilteredItems(List<MediaItem> items) {
    var filtered = items;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        final query = _searchQuery.toLowerCase();
        return item.title.toLowerCase().contains(query) ||
               (item.artist?.toLowerCase().contains(query) ?? false) ||
               (item.album?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply type filter
    if (_filterType != 'all') {
      filtered = filtered.where((item) {
        if (_filterType == 'audio') return !item.isVideo;
        if (_filterType == 'video') return item.isVideo;
        return true;
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'name':
          return a.title.compareTo(b.title);
        case 'artist':
          final artistA = a.artist ?? '';
          final artistB = b.artist ?? '';
          return artistA.compareTo(artistB);
        case 'dateAdded':
          return b.dateAdded.compareTo(a.dateAdded);
        default:
          return 0;
      }
    });

    return filtered;
  }

  Future<void> _addMedia(BuildContext context, AppProvider app) async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['mp3', 'wav', 'm4a', 'aac', 'mp4', 'mkv', 'mov', 'avi'],
    );
    
    if (result != null) {
      for (final file in result.files) {
        final path = file.path;
        if (path == null) continue;
        
        final bool isVideo = ['mp4', 'mkv', 'mov', 'avi'].contains(file.extension?.toLowerCase());
        
        await app.addMedia(
          title: file.name,
          path: path,
          isVideo: isVideo,
        );
      }
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added ${result.files.length} media file(s)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _handleMediaAction(
    BuildContext context,
    AppProvider app,
    MediaItem item,
    String action,
  ) async {
    switch (action) {
      case 'play':
        await app.playMedia(item.id);
        // Navigate to the appropriate player
        if (context.mounted) {
          _navigateToPlayer(context, item);
        }
        break;
      case 'info':
        _showMediaInfoDialog(context, item);
        break;
      case 'delete':
        await _showDeleteConfirmation(context, app, item);
        break;
      default:
        if (action.startsWith('add_to_')) {
          final playlistId = action.substring(7);
          await app.addToPlaylist(playlistId, item.id);
          if (context.mounted) {
            final playlist = app.playlists.firstWhere((p) => p.id == playlistId);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Added to ${playlist.name}'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
    }
  }

  void _navigateToPlayer(BuildContext context, MediaItem item) {
    if (item.isVideo) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerView(item: item),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SongPlayerView(item: item),
        ),
      );
    }
  }

  void _showMediaInfoDialog(BuildContext context, MediaItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Media Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Title', item.title),
            if (item.artist != null) _buildInfoRow('Artist', item.artist!),
            if (item.album != null) _buildInfoRow('Album', item.album!),
            if (item.genre != null) _buildInfoRow('Genre', item.genre!),
            _buildInfoRow('Type', item.isVideo ? 'Video' : 'Audio'),
            if (item.duration != null) _buildInfoRow('Duration', _formatDuration(item.duration!)),
            _buildInfoRow('Added', _formatDate(item.dateAdded)),
            _buildInfoRow('Path', item.path),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context, AppProvider app, MediaItem item) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Media?'),
        content: Text(
          'Are you sure you want to delete "${item.title}"? This will also remove it from all playlists.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      await app.removeMedia(item.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Media deleted'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    final hours = duration.inHours;
    return hours > 0 ? '$hours:$minutes:$seconds' : '$minutes:$seconds';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}


