import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../models/playback_state.dart';
import '../providers/app_provider.dart';
import '../models/playlist.dart';
import '../models/media_item.dart';
import '../widgets/floating_instruments.dart';
import '../widgets/enhanced_app_header.dart';
import 'song_player_view.dart';
import 'video_player_view.dart';

class PlaylistViewScreen extends StatelessWidget {
  final String playlistId;
  const PlaylistViewScreen({super.key, required this.playlistId});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, app, _) {
        final playlist = app.playlists.firstWhere((p) => p.id == playlistId);
        final items = app.getPlaylistItems(playlistId);

        return Scaffold(
          body: Stack(
            children: [
              // Background floating instruments
              const FloatingInstruments(
                count: 20,
                speed: 0.5,
                enableParallax: true,
                instruments: ['🎵', '🎶', '🎼', '🎤', '🎧', '🎸', '🎹', '🎷'],
              ),

              // Main content
              Column(
                children: [
                  // Enhanced app header
                  EnhancedAppHeader(
                    title: playlist.name,
                    showMusicVisualizer: false,
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.play_arrow, color: Colors.white),
                        onPressed: () =>
                            _handleAction(context, app, playlist, 'play'),
                      ),
                      IconButton(
                        icon: const Icon(Icons.shuffle, color: Colors.white),
                        onPressed: () =>
                            _handleAction(context, app, playlist, 'shuffle'),
                      ),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                        onSelected: (value) =>
                            _handleAction(context, app, playlist, value),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit),
                                SizedBox(width: 8),
                                Text('Edit Playlist'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'export',
                            child: Row(
                              children: [
                                Icon(Icons.share),
                                SizedBox(width: 8),
                                Text('Export Playlist'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Playlist description
                  if (playlist.description != null)
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border:
                            Border.all(color: Colors.white.withOpacity(0.2)),
                      ),
                      child: Text(
                        playlist.description!,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ),

                  // Items count and play buttons
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${items.length} ${items.length == 1 ? 'item' : 'items'}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withOpacity(0.2),
                                Colors.white.withOpacity(0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                                color: Colors.white.withOpacity(0.3)),
                          ),
                          child: TextButton.icon(
                            onPressed: () =>
                                _handleAction(context, app, playlist, 'play'),
                            icon: const Icon(Icons.play_arrow,
                                color: Colors.white),
                            label: const Text('Play All',
                                style: TextStyle(color: Colors.white)),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withOpacity(0.2),
                                Colors.white.withOpacity(0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                                color: Colors.white.withOpacity(0.3)),
                          ),
                          child: TextButton.icon(
                            onPressed: () => _handleAction(
                                context, app, playlist, 'shuffle'),
                            icon:
                                const Icon(Icons.shuffle, color: Colors.white),
                            label: const Text('Shuffle',
                                style: TextStyle(color: Colors.white)),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Media items list
                  Expanded(
                    child: items.isEmpty
                        ? _buildEmptyState(context, playlist)
                        : ReorderableListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: items.length,
                            onReorder: (oldIndex, newIndex) {
                              app.reorderInPlaylist(
                                  playlistId, oldIndex, newIndex);
                            },
                            itemBuilder: (context, index) {
                              final item = items[index];
                              final isCurrentTrack =
                                  app.playbackState.currentMediaId == item.id &&
                                      app.playbackState.currentPlaylistId ==
                                          playlistId;
                              return _buildMediaItem(
                                  context, app, item, index, isCurrentTrack);
                            },
                          ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, Playlist playlist) {
    return Stack(
      children: [
        // Additional floating instruments for empty state
        const FloatingInstruments(
          count: 6,
          speed: 0.7,
          instruments: ['🎸', '🎹', '🎷', '🥁', '🎻', '🎺'],
        ),

        // Main content
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: _parseColor(playlist.coverColor).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: _parseColor(playlist.coverColor).withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.playlist_add,
                  size: 48,
                  color: _parseColor(playlist.coverColor),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                '${playlist.name} is empty',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add some media to get started',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 24),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: TextButton.icon(
                  onPressed: () {
                    // Navigate to library to add media
                    Navigator.pop(context);
                  },
                  icon: const Icon(Icons.add, color: Colors.white),
                  label: const Text('Add Media',
                      style: TextStyle(color: Colors.white)),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMediaItem(BuildContext context, AppProvider app, MediaItem item,
      int index, bool isCurrentTrack) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 200 + (index * 50)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                color: isCurrentTrack
                    ? Colors.white.withOpacity(0.2)
                    : Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isCurrentTrack
                      ? Colors.white.withOpacity(0.4)
                      : Colors.white.withOpacity(0.1),
                ),
                boxShadow: isCurrentTrack
                    ? [
                        BoxShadow(
                          color: Colors.white.withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ]
                    : null,
              ),
              child: ListTile(
                leading: Container(
                  decoration: BoxDecoration(
                    color: item.isVideo
                        ? Colors.red.withOpacity(0.2)
                        : Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: (item.isVideo ? Colors.red : Colors.blue)
                            .withOpacity(0.2),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Icon(
                    item.isVideo ? Icons.videocam : Icons.music_note,
                    color: item.isVideo ? Colors.red : Colors.blue,
                  ),
                ),
                title: Text(
                  item.displayTitle,
                  style: TextStyle(
                    fontWeight:
                        isCurrentTrack ? FontWeight.w600 : FontWeight.normal,
                    color: Colors.white,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.displaySubtitle,
                      style: TextStyle(color: Colors.white.withOpacity(0.7)),
                    ),
                    if (item.duration != null)
                      Text(
                        _formatDuration(item.duration!),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isCurrentTrack)
                      const Icon(Icons.play_arrow, color: Colors.white, size: 20),
                    PopupMenuButton<String>(
                      onSelected: (value) =>
                          _showMediaItemOptions(context, app, item, value),
                      icon: Icon(Icons.more_vert,
                          color: Colors.white.withOpacity(0.8)),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'play_now',
                          child: Row(
                            children: [
                              Icon(Icons.play_arrow),
                              SizedBox(width: 8),
                              Text('Play Now'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'add_to_playlist',
                          child: Row(
                            children: [
                              Icon(Icons.playlist_add),
                              SizedBox(width: 8),
                              Text('Add to Playlist'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'media_info',
                          child: Row(
                            children: [
                              Icon(Icons.info),
                              SizedBox(width: 8),
                              Text('Media Info'),
                            ],
                          ),
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'remove',
                          child: Row(
                            children: [
                              Icon(Icons.remove_circle, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Remove from Playlist',
                                  style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                onTap: () => _navigateToPlayer(context, app, item, index),
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToPlayer(BuildContext context, AppProvider app, MediaItem item, int index) {
    // First set the playlist to play from this index
    app.playPlaylist(playlistId, startIndex: index);

    // Then navigate to the appropriate player
    if (item.isVideo) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerView(item: item),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SongPlayerView(item: item),
        ),
      );
    }
  }

  void _showMediaItemOptions(
      BuildContext context, AppProvider app, MediaItem item, String action) {
    switch (action) {
      case 'play_now':
        app.playMedia(item.id);
        // Navigate to the appropriate player
        if (item.isVideo) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VideoPlayerView(item: item),
            ),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SongPlayerView(item: item),
            ),
          );
        }
        break;
      case 'add_to_playlist':
        _showAddToPlaylistDialog(context, app, item);
        break;
      case 'media_info':
        _showMediaInfoDialog(context, item);
        break;
      case 'remove':
        app.removeFromPlaylist(playlistId, item.id);
        break;
    }
  }

  void _showAddToPlaylistDialog(
      BuildContext context, AppProvider app, MediaItem item) {
    final otherPlaylists =
        app.playlists.where((p) => p.id != playlistId).toList();

    if (otherPlaylists.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No other playlists available')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add to Playlist'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: otherPlaylists.map((playlist) {
            return ListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _parseColor(playlist.coverColor),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.playlist_play,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              title: Text(playlist.name),
              subtitle: Text('${playlist.itemCount} items'),
              onTap: () {
                app.addToPlaylist(playlist.id, item.id);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Added to ${playlist.name}'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showMediaInfoDialog(BuildContext context, MediaItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Media Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Title', item.title),
            if (item.artist != null) _buildInfoRow('Artist', item.artist!),
            if (item.album != null) _buildInfoRow('Album', item.album!),
            if (item.genre != null) _buildInfoRow('Genre', item.genre!),
            _buildInfoRow('Type', item.isVideo ? 'Video' : 'Audio'),
            if (item.duration != null)
              _buildInfoRow('Duration', _formatDuration(item.duration!)),
            _buildInfoRow('Added', _formatDate(item.dateAdded)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAction(BuildContext context, AppProvider app,
      Playlist playlist, String action) async {
    switch (action) {
      case 'play':
        await app.playPlaylist(playlist.id);
        break;
      case 'shuffle':
        await app.setPlaybackMode(PlaybackMode.shuffle);
        await app.playPlaylist(playlist.id);
        break;
      case 'edit':
        // TODO: Implement edit playlist functionality
        break;
      case 'export':
        // TODO: Implement export playlist functionality
        break;
    }
  }

  Color _parseColor(String? colorString) {
    if (colorString == null) return Colors.blue;
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.blue;
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    final hours = duration.inHours;
    return hours > 0 ? '$hours:$minutes:$seconds' : '$minutes:$seconds';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
